import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import ParticleBackground from './ParticleBackground';
import KuberaAnimation from './KuberaAnimation';
import { useCart } from '../context/CartContext';

const CheckoutPage = () => {
  const { cart, getCartTotal, getCartItemCount, clearCart } = useCart();
  const navigate = useNavigate();
  
  const [customerInfo, setCustomerInfo] = useState({
    fullName: '',
    phone: '',
    email: '',
    address: '',
    city: '',
    postalCode: '',
    specialInstructions: ''
  });
  
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState('');

  // Redirect to cart if empty
  useEffect(() => {
    if (cart.items.length === 0) {
      navigate('/cart');
    }
  }, [cart.items.length, navigate]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setCustomerInfo(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }

    // Clear submit error
    if (submitError) {
      setSubmitError('');
    }
  };

  const handleInputBlur = (e) => {
    const { name, value } = e.target;

    // Validate individual field on blur
    validateField(name, value);
  };

  const validateField = (fieldName, value) => {
    let error = '';

    switch (fieldName) {
      case 'fullName':
        if (!value.trim()) {
          error = 'සම්පූර්ණ නම අවශ්‍යයි';
        } else if (value.trim().length < 2) {
          error = 'නම අවම වශයෙන් අකුරු 2ක් තිබිය යුතුය';
        } else if (!/^[a-zA-Zඅ-ෆ\s]+$/.test(value.trim())) {
          error = 'නමේ වලංගු අකුරු පමණක් භාවිතා කරන්න';
        }
        break;

      case 'phone':
        if (!value.trim()) {
          error = 'දුරකථන අංකය අවශ්‍යයි';
        } else if (!/^[0-9+\-\s()]{10,}$/.test(value.trim())) {
          error = 'වලංගු දුරකථන අංකයක් ඇතුළත් කරන්න (අවම අංක 10ක්)';
        }
        break;

      case 'email':
        if (value.trim() && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value.trim())) {
          error = 'වලංගු ඊමේල් ලිපිනයක් ඇතුළත් කරන්න';
        }
        break;

      case 'address':
        if (!value.trim()) {
          error = 'ලිපිනය අවශ්‍යයි';
        } else if (value.trim().length < 10) {
          error = 'සම්පූර්ණ ලිපිනය ඇතුළත් කරන්න (අවම අකුරු 10ක්)';
        }
        break;

      case 'city':
        if (!value.trim()) {
          error = 'නගරය අවශ්‍යයි';
        } else if (value.trim().length < 2) {
          error = 'වලංගු නගර නාමයක් ඇතුළත් කරන්න';
        }
        break;

      case 'postalCode':
        if (value.trim() && !/^[0-9]{5}$/.test(value.trim())) {
          error = 'වලංගු තැපැල් කේතයක් ඇතුළත් කරන්න (අංක 5ක්)';
        }
        break;

      default:
        break;
    }

    if (error) {
      setErrors(prev => ({
        ...prev,
        [fieldName]: error
      }));
    } else {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[fieldName];
        return newErrors;
      });
    }

    return !error;
  };

  const validateForm = () => {
    let isValid = true;
    const requiredFields = ['fullName', 'phone', 'address', 'city'];
    const optionalFields = ['email', 'postalCode'];

    // Validate all fields
    [...requiredFields, ...optionalFields].forEach(field => {
      const fieldValue = customerInfo[field] || '';
      const fieldValid = validateField(field, fieldValue);
      if (!fieldValid) {
        isValid = false;
      }
    });

    return isValid;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmitError('');

    if (!validateForm()) {
      setSubmitError('කරුණාකර සියලුම අවශ්‍ය ක්ෂේත්‍ර නිවැරදිව පුරවන්න');
      // Scroll to first error
      const firstErrorField = document.querySelector('.error');
      if (firstErrorField) {
        firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
        firstErrorField.focus();
      }
      return;
    }

    setIsSubmitting(true);

    try {
      // Validate cart is not empty (double check)
      if (cart.items.length === 0) {
        throw new Error('කාර්ට් එක හිස්ය');
      }

      // Prepare order data for API
      const orderData = {
        customerInfo: {
          fullName: customerInfo.fullName.trim(),
          phone: customerInfo.phone.trim(),
          email: customerInfo.email.trim(),
          address: customerInfo.address.trim(),
          city: customerInfo.city.trim(),
          postalCode: customerInfo.postalCode.trim(),
          specialInstructions: customerInfo.specialInstructions.trim()
        },
        items: cart.items.map(item => ({
          id: item.id,
          name: item.name,
          price: item.price,
          quantity: item.quantity,
          image: item.image
        })),
        totalAmount: getCartTotal()
      };

      // Validate order data
      if (orderData.totalAmount <= 0) {
        throw new Error('වලංගු නොවන ඇණවුම් මුදල');
      }

      // Submit order to server
      const response = await fetch('/api/orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(orderData)
      });

      const result = await response.json();

      if (!response.ok || !result.success) {
        throw new Error(result.error || 'ඇණවුම ගබඩා කිරීමේදී දෝෂයක් ඇතිවිය');
      }

      // Save order to localStorage as backup
      try {
        const order = {
          id: result.orderId,
          items: cart.items,
          customerInfo: orderData.customerInfo,
          total: orderData.totalAmount,
          itemCount: getCartItemCount(),
          orderDate: new Date().toISOString(),
          status: 'pending',
          paymentMethod: 'cod'
        };

        const existingOrders = JSON.parse(localStorage.getItem('kuberaOrders') || '[]');
        existingOrders.push(order);
        localStorage.setItem('kuberaOrders', JSON.stringify(existingOrders));
      } catch (storageError) {
        console.warn('Failed to save order to localStorage:', storageError);
      }

      // Clear cart
      clearCart();

      // Navigate to confirmation page
      navigate(`/order-confirmation/${result.orderId}`, {
        state: {
          orderId: result.orderId,
          telegramNotificationSent: result.telegramNotificationSent
        }
      });

    } catch (error) {
      console.error('Order submission error:', error);
      setSubmitError(
        error.message || 'ඇණවුම ගබඩා කිරීමේදී දෝෂයක් ඇතිවිය. කරුණාකර නැවත උත්සාහ කරන්න.'
      );

      // Scroll to error message
      setTimeout(() => {
        const errorElement = document.querySelector('.submit-error');
        if (errorElement) {
          errorElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }, 100);
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatPrice = (price) => {
    return `රු. ${price.toLocaleString()}`;
  };

  if (cart.items.length === 0) {
    return null; // Will redirect via useEffect
  }

  return (
    <div className="checkout-page">
      <ParticleBackground />
      <KuberaAnimation />

      {/* Back Button */}
      <Link to="/cart" className="back-button dark-glass-card">
        <span className="back-arrow">←</span>
        <span>කාර්ට් එකට ආපසු</span>
      </Link>

      {/* Page Header */}
      <div className="checkout-header">
        <h1 className="checkout-title">ගෙවීම් පිටුව</h1>
        <p className="checkout-subtitle">
          ඔබගේ ඇණවුම සම්පූර්ණ කිරීම සඳහා තොරතුරු ඇතුළත් කරන්න
        </p>
      </div>

      <div className="checkout-container">
        {/* Customer Information Form */}
        <div className="checkout-form-section">
          <div className="form-card dark-glass-card">
            <div className="card-glow"></div>
            <div className="card-shine"></div>
            
            <h3 className="form-title">ගනුදෙනුකරු තොරතුරු</h3>
            
            <form onSubmit={handleSubmit} className="checkout-form">
              {submitError && (
                <div className="submit-error error-state">
                  <span className="error-icon">⚠️</span>
                  <span>{submitError}</span>
                </div>
              )}

              <div className="form-group">
                <label htmlFor="fullName">සම්පූර්ණ නම *</label>
                <input
                  type="text"
                  id="fullName"
                  name="fullName"
                  value={customerInfo.fullName}
                  onChange={handleInputChange}
                  onBlur={handleInputBlur}
                  className={errors.fullName ? 'error' : ''}
                  placeholder="ඔබගේ සම්පූර්ණ නම ඇතුළත් කරන්න"
                  aria-describedby={errors.fullName ? 'fullName-error' : undefined}
                />
                {errors.fullName && (
                  <span id="fullName-error" className="error-message" role="alert">
                    {errors.fullName}
                  </span>
                )}
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="phone">දුරකථන අංකය *</label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={customerInfo.phone}
                    onChange={handleInputChange}
                    onBlur={handleInputBlur}
                    className={errors.phone ? 'error' : ''}
                    placeholder="************"
                    aria-describedby={errors.phone ? 'phone-error' : undefined}
                  />
                  {errors.phone && (
                    <span id="phone-error" className="error-message" role="alert">
                      {errors.phone}
                    </span>
                  )}
                </div>

                <div className="form-group">
                  <label htmlFor="email">ඊමේල් ලිපිනය</label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={customerInfo.email}
                    onChange={handleInputChange}
                    onBlur={handleInputBlur}
                    className={errors.email ? 'error' : ''}
                    placeholder="<EMAIL>"
                    aria-describedby={errors.email ? 'email-error' : undefined}
                  />
                  {errors.email && (
                    <span id="email-error" className="error-message" role="alert">
                      {errors.email}
                    </span>
                  )}
                </div>
              </div>

              <div className="form-group">
                <label htmlFor="address">ලිපිනය *</label>
                <textarea
                  id="address"
                  name="address"
                  value={customerInfo.address}
                  onChange={handleInputChange}
                  onBlur={handleInputBlur}
                  className={errors.address ? 'error' : ''}
                  placeholder="ඔබගේ සම්පූර්ණ ලිපිනය ඇතුළත් කරන්න"
                  rows="3"
                  aria-describedby={errors.address ? 'address-error' : undefined}
                />
                {errors.address && (
                  <span id="address-error" className="error-message" role="alert">
                    {errors.address}
                  </span>
                )}
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="city">නගරය *</label>
                  <input
                    type="text"
                    id="city"
                    name="city"
                    value={customerInfo.city}
                    onChange={handleInputChange}
                    onBlur={handleInputBlur}
                    className={errors.city ? 'error' : ''}
                    placeholder="කොළඹ"
                    aria-describedby={errors.city ? 'city-error' : undefined}
                  />
                  {errors.city && (
                    <span id="city-error" className="error-message" role="alert">
                      {errors.city}
                    </span>
                  )}
                </div>

                <div className="form-group">
                  <label htmlFor="postalCode">තැපැල් කේතය</label>
                  <input
                    type="text"
                    id="postalCode"
                    name="postalCode"
                    value={customerInfo.postalCode}
                    onChange={handleInputChange}
                    onBlur={handleInputBlur}
                    className={errors.postalCode ? 'error' : ''}
                    placeholder="00100"
                    aria-describedby={errors.postalCode ? 'postalCode-error' : undefined}
                  />
                  {errors.postalCode && (
                    <span id="postalCode-error" className="error-message" role="alert">
                      {errors.postalCode}
                    </span>
                  )}
                </div>
              </div>

              <div className="form-group">
                <label htmlFor="specialInstructions">විශේෂ උපදෙස්</label>
                <textarea
                  id="specialInstructions"
                  name="specialInstructions"
                  value={customerInfo.specialInstructions}
                  onChange={handleInputChange}
                  placeholder="ගෙන්වා දීම සඳහා විශේෂ උපදෙස් (වෛකල්පික)"
                  rows="2"
                />
              </div>

              <div className="payment-method-info">
                <h4>ගෙවීමේ ක්‍රමය</h4>
                <div className="payment-option">
                  <span className="payment-icon">💰</span>
                  <div className="payment-details">
                    <strong>Cash on Delivery (COD)</strong>
                    <p>භාණ්ඩ ලැබෙන විට ගෙවන්න</p>
                  </div>
                </div>
              </div>

              <button 
                type="submit" 
                className="submit-order-btn dark-glass-card primary"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <span className="loading-spinner"></span>
                    <span>ඇණවුම ගබඩා කරමින්...</span>
                  </>
                ) : (
                  <>
                    <span className="btn-icon">✅</span>
                    <span>ඇණවුම තහවුරු කරන්න</span>
                  </>
                )}
              </button>
            </form>
          </div>
        </div>

        {/* Order Summary Section */}
        <div className="order-summary-section">
          <div className="summary-card dark-glass-card">
            <div className="card-glow"></div>
            <div className="card-shine"></div>

            <h3 className="summary-title">ඇණවුම් සාරාංශය</h3>

            <div className="order-items">
              {cart.items.map((item) => (
                <div key={item.id} className="order-item">
                  <div className="item-image">
                    <img
                      src={item.image}
                      alt={item.name}
                      onError={(e) => {
                        e.target.src = '/god.jpg';
                      }}
                    />
                  </div>
                  <div className="item-details">
                    <h4 className="item-name">{item.name}</h4>
                    <div className="item-quantity">ප්‍රමාණය: {item.quantity}</div>
                    <div className="item-price">{formatPrice(item.price)}</div>
                  </div>
                  <div className="item-total">
                    {formatPrice(item.price * item.quantity)}
                  </div>
                </div>
              ))}
            </div>

            <div className="order-totals">
              <div className="total-row">
                <span>කාඩ්පත් ගණන:</span>
                <span>{getCartItemCount()}</span>
              </div>

              <div className="total-row">
                <span>උප එකතුව:</span>
                <span>{formatPrice(getCartTotal())}</span>
              </div>

              <div className="total-row">
                <span>ගෙන්වා දීමේ ගාස්තුව:</span>
                <span>නොමිලේ</span>
              </div>

              <div className="total-divider"></div>

              <div className="total-row final-total">
                <span>මුළු එකතුව:</span>
                <span>{formatPrice(getCartTotal())}</span>
              </div>
            </div>

            <div className="delivery-info">
              <div className="delivery-item">
                <span className="delivery-icon">🚚</span>
                <div className="delivery-details">
                  <strong>ගෙන්වා දීම</strong>
                  <p>2-3 වැඩ කරන දින</p>
                </div>
              </div>

              <div className="delivery-item">
                <span className="delivery-icon">📞</span>
                <div className="delivery-details">
                  <strong>සම්බන්ධතාව</strong>
                  <p>ගෙන්වා දීමට පෙර අමතනු ලැබේ</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer Blessing */}
      <div className="checkout-footer">
        <div className="divine-blessing">
          <span className="blessing-text">🙏 කුබේර දෙවියන්ගේ ආශීර්වාදය ඔබ සමඟ වේවා 🙏</span>
        </div>
      </div>
    </div>
  );
};

export default CheckoutPage;
