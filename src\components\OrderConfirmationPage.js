import React, { useState, useEffect } from 'react';
import { useParams, useLocation, Link } from 'react-router-dom';
import ParticleBackground from './ParticleBackground';
import KuberaAnimation from './KuberaAnimation';

const OrderConfirmationPage = () => {
  const { orderId } = useParams();
  const location = useLocation();
  const [order, setOrder] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [telegramNotificationSent, setTelegramNotificationSent] = useState(false);

  useEffect(() => {
    const fetchOrder = async () => {
      try {
        // Check if we have notification status from navigation state
        if (location.state && location.state.telegramNotificationSent) {
          setTelegramNotificationSent(location.state.telegramNotificationSent);
        }

        // Try to fetch order from API
        const response = await fetch(`/api/orders/${orderId}`);
        const result = await response.json();

        if (response.ok && result.success) {
          setOrder(result.data);
        } else {
          // Fallback: try to find order in localStorage
          const savedOrders = JSON.parse(localStorage.getItem('kuberaOrders') || '[]');
          const foundOrder = savedOrders.find(o => o.id === orderId || o.orderId === orderId);

          if (foundOrder) {
            setOrder(foundOrder);
          } else {
            setError('ඇණවුම සොයා ගත නොහැකි විය');
          }
        }
      } catch (fetchError) {
        console.error('Error fetching order:', fetchError);
        // Fallback: try localStorage
        try {
          const savedOrders = JSON.parse(localStorage.getItem('kuberaOrders') || '[]');
          const foundOrder = savedOrders.find(o => o.id === orderId || o.orderId === orderId);

          if (foundOrder) {
            setOrder(foundOrder);
          } else {
            setError('ඇණවුම සොයා ගත නොහැකි විය');
          }
        } catch (storageError) {
          setError('ඇණවුම් තොරතුරු ලබා ගැනීමේදී දෝෂයක් ඇතිවිය');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchOrder();
  }, [orderId, location.state]);

  const formatPrice = (price) => {
    return `රු. ${price.toLocaleString()}`;
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('si-LK', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="order-confirmation-page">
        <ParticleBackground />
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>ඇණවුම් තොරතුරු ලබා ගනිමින්...</p>
        </div>
      </div>
    );
  }

  if (error || !order) {
    return (
      <div className="order-confirmation-page">
        <ParticleBackground />
        <KuberaAnimation />

        <div className="error-container">
          <div className="error-card dark-glass-card">
            <div className="card-glow"></div>
            <div className="card-shine"></div>

            <div className="error-icon">❌</div>
            <h2 className="error-title">{error || 'ඇණවුම සොයා ගත නොහැක'}</h2>
            <p className="error-message">
              {error ? 'ඇණවුම් තොරතුරු ලබා ගැනීමේදී දෝෂයක් ඇතිවිය.' : 'ඉල්ලූ ඇණවුම සොයා ගත නොහැක. කරුණාකර ඇණවුම් අංකය පරීක්ෂා කර නැවත උත්සාහ කරන්න.'}
            </p>
            <p className="order-id">ඇණවුම් අංකය: {orderId}</p>

            <div className="error-actions">
              <Link to="/" className="home-btn dark-glass-card">
                <span className="btn-icon">🏠</span>
                <span>මුල් පිටුවට යන්න</span>
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="order-confirmation-page">
      <ParticleBackground />
      <KuberaAnimation />

      {/* Success Header */}
      <div className="confirmation-header">
        <div className="success-icon">✅</div>
        <h1 className="confirmation-title">ඇණවුම සාර්ථකව ලැබිණි!</h1>
        <p className="confirmation-subtitle">
          ඔබගේ ඇණවුම අපට ලැබී ඇත. ඉක්මනින්ම ඔබ සමඟ සම්බන්ධ වෙමු.
        </p>
      </div>

      <div className="confirmation-container">
        {/* Order Details Card */}
        <div className="order-details-card dark-glass-card">
          <div className="card-glow"></div>
          <div className="card-shine"></div>
          
          <h3 className="card-title">ඇණවුම් විස්තර</h3>
          
          <div className="order-info">
            <div className="info-row">
              <span className="info-label">ඇණවුම් අංකය:</span>
              <span className="info-value order-id">{order.id}</span>
            </div>
            
            <div className="info-row">
              <span className="info-label">ඇණවුම් දිනය:</span>
              <span className="info-value">{formatDate(order.orderDate)}</span>
            </div>
            
            <div className="info-row">
              <span className="info-label">ගෙවීමේ ක්‍රමය:</span>
              <span className="info-value">Cash on Delivery (COD)</span>
            </div>
            
            <div className="info-row">
              <span className="info-label">ඇණවුම් තත්වය:</span>
              <span className="info-value status-pending">සකස් කරමින්</span>
            </div>
          </div>
        </div>

        {/* Customer Information Card */}
        <div className="customer-info-card dark-glass-card">
          <div className="card-glow"></div>
          <div className="card-shine"></div>
          
          <h3 className="card-title">ගනුදෙනුකරු තොරතුරු</h3>
          
          <div className="customer-details">
            <div className="detail-row">
              <span className="detail-icon">👤</span>
              <div className="detail-content">
                <strong>නම:</strong> {order.customerInfo.fullName}
              </div>
            </div>
            
            <div className="detail-row">
              <span className="detail-icon">📞</span>
              <div className="detail-content">
                <strong>දුරකථනය:</strong> {order.customerInfo.phone}
              </div>
            </div>
            
            {order.customerInfo.email && (
              <div className="detail-row">
                <span className="detail-icon">📧</span>
                <div className="detail-content">
                  <strong>ඊමේල්:</strong> {order.customerInfo.email}
                </div>
              </div>
            )}
            
            <div className="detail-row">
              <span className="detail-icon">📍</span>
              <div className="detail-content">
                <strong>ලිපිනය:</strong>
                <div className="address">
                  {order.customerInfo.address}<br/>
                  {order.customerInfo.city}
                  {order.customerInfo.postalCode && ` ${order.customerInfo.postalCode}`}
                </div>
              </div>
            </div>
            
            {order.customerInfo.specialInstructions && (
              <div className="detail-row">
                <span className="detail-icon">📝</span>
                <div className="detail-content">
                  <strong>විශේෂ උපදෙස්:</strong> {order.customerInfo.specialInstructions}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Order Items Card */}
        <div className="order-items-card dark-glass-card">
          <div className="card-glow"></div>
          <div className="card-shine"></div>
          
          <h3 className="card-title">ඇණවුම් කරන ලද කාඩ්පත්</h3>
          
          <div className="ordered-items">
            {order.items.map((item) => (
              <div key={item.id} className="ordered-item">
                <div className="item-image">
                  <img 
                    src={item.image} 
                    alt={item.name}
                    onError={(e) => {
                      e.target.src = '/god.jpg';
                    }}
                  />
                </div>
                
                <div className="item-details">
                  <h4 className="item-name">{item.name}</h4>
                  <div className="item-quantity">ප්‍රමාණය: {item.quantity}</div>
                  <div className="item-unit-price">එක් කාඩ්පතක් මිල: {formatPrice(item.price)}</div>
                </div>
                
                <div className="item-total">
                  {formatPrice(item.price * item.quantity)}
                </div>
              </div>
            ))}
          </div>
          
          <div className="order-summary">
            <div className="summary-row">
              <span>කාඩ්පත් ගණන:</span>
              <span>{order.itemCount}</span>
            </div>
            
            <div className="summary-row">
              <span>ගෙන්වා දීමේ ගාස්තුව:</span>
              <span>නොමිලේ</span>
            </div>
            
            <div className="summary-divider"></div>
            
            <div className="summary-row total-row">
              <span>මුළු එකතුව:</span>
              <span>{formatPrice(order.total)}</span>
            </div>
          </div>
        </div>

        {/* Next Steps Card */}
        <div className="next-steps-card dark-glass-card">
          <div className="card-glow"></div>
          <div className="card-shine"></div>
          
          <h3 className="card-title">ඊළඟ පියවර</h3>
          
          <div className="steps-list">
            <div className="step-item">
              <div className="step-number">1</div>
              <div className="step-content">
                <strong>ඇණවුම් තහවුරු කිරීම</strong>
                <p>අපගේ කණ්ඩායම ඔබගේ ඇණවුම සකස් කර 24 පැය ඇතුළත ඔබ සමඟ සම්බන්ධ වේ.</p>
              </div>
            </div>
            
            <div className="step-item">
              <div className="step-number">2</div>
              <div className="step-content">
                <strong>ගෙන්වා දීම</strong>
                <p>ඔබගේ කුබේර කාඩ්පත් 2-3 වැඩ කරන දිනවලදී ඔබගේ ලිපිනයට ගෙන්වා දෙනු ලැබේ.</p>
              </div>
            </div>
            
            <div className="step-item">
              <div className="step-number">3</div>
              <div className="step-content">
                <strong>ගෙවීම</strong>
                <p>භාණ්ඩ ලැබෙන විට Cash on Delivery ක්‍රමයෙන් ගෙවීම කරන්න.</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="confirmation-actions">
        <Link to="/kubera-cards" className="continue-shopping-btn dark-glass-card">
          <span className="btn-icon">🛒</span>
          <span>තවත් කාඩ්පත් මිලදී ගන්න</span>
        </Link>
        
        <Link to="/" className="home-btn dark-glass-card">
          <span className="btn-icon">🏠</span>
          <span>මුල් පිටුවට යන්න</span>
        </Link>
      </div>

      {/* Footer Blessing */}
      <div className="confirmation-footer">
        <div className="divine-blessing">
          <span className="blessing-text">🙏 කුබේර දෙවියන්ගේ ආශීර්වාදය ඔබ සමඟ වේවා 🙏</span>
        </div>
      </div>
    </div>
  );
};

export default OrderConfirmationPage;
