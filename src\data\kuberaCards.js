// Kubera Cards Product Data
// Spiritual cards for wealth, prosperity, and divine blessings

export const kuberaCards = [
  {
    id: 'kubera-wealth-card',
    name: 'කුබේර ධන කාඩ්පත',
    englishName: 'Kubera Wealth Card',
    price: 2500,
    originalPrice: 3500,
    discount: 29,
    category: 'wealth',
    description: 'කුබේර දෙවියන්ගේ විශේෂ ආශීර්වාදය සහිත ධන ආකර්ෂණ කාඩ්පත. ඔබගේ ජීවිතයට ධනය සහ සමෘද්ධිය ගෙන එන අධ්‍යාත්මික ශක්තියක් ඇති කාඩ්පතකි.',
    longDescription: `
      කුබේර ධන කාඩ්පත යනු පුරාණ වෛදික සම්ප්‍රදායන් මත පදනම්ව සකස් කරන ලද විශේෂ අධ්‍යාත්මික කාඩ්පතකි. 
      
      මෙම කාඩ්පත ඔබගේ:
      • ධන ආකර්ෂණ ශක්තිය වැඩි කරයි
      • ව්‍යාපාරික සාර්ථකත්වය ගෙන දෙයි
      • මූලික බාධක ඉවත් කරයි
      • ආර්ථික ස්ථාවරත්වය ලබා දෙයි
      
      විශේෂ ලක්ෂණ:
      • කුබේර මන්ත්‍ර සහිත
      • ස්වර්ණ පැහැති විශේෂ මුද්‍රණය
      • අධ්‍යාත්මික ශක්තියෙන් ආශීර්වාද කරන ලද
      • දිගු කාලීන භාවිතයට සුදුසු
    `,
    benefits: [
      'ධන ආකර්ෂණ ශක්තිය වැඩි කිරීම',
      'ව්‍යාපාරික අවස්ථා ලැබීම',
      'ණය බරින් මිදීම',
      'ආර්ථික ස්ථාවරත්වය',
      'අනපේක්ෂිත ආදායම් මාර්ග'
    ],
    specifications: {
      material: 'විශේෂ ගුණාත්මක කාඩ්බෝඩ්',
      size: '8.5cm x 5.5cm',
      thickness: '0.8mm',
      finish: 'ස්වර්ණ පැහැති ලැමිනේට්',
      packaging: 'ආරක්ෂිත කොටුව සහිත'
    },
    images: [
      '/god.jpg',
      '/god.jpg',
      '/god.jpg'
    ],
    inStock: true,
    featured: true,
    rating: 4.8,
    reviewCount: 156
  },
  {
    id: 'kubera-prosperity-card',
    name: 'කුබේර සමෘද්ධි කාඩ්පත',
    englishName: 'Kubera Prosperity Card',
    price: 3000,
    originalPrice: 4000,
    discount: 25,
    category: 'prosperity',
    description: 'සමස්ත ජීවිත සමෘද්ධිය සඳහා විශේෂයෙන් නිර්මාණය කරන ලද කුබේර කාඩ්පත. ධනය, සෞඛ්‍යය සහ සතුට යන තුනම ගෙන දෙන ශක්තිමත් කාඩ්පතකි.',
    longDescription: `
      කුබේර සමෘද්ධි කාඩ්පත යනු සම්පූර්ණ ජීවිත සමෘද්ධිය සඳහා නිර්මාණය කරන ලද අද්විතීය අධ්‍යාත්මික මෙවලමකි.
      
      මෙම කාඩ්පත ඔබට ගෙන දෙයි:
      • සම්පූර්ණ ජීවිත සමෘද්ධිය
      • පවුලේ සතුට සහ සාමය
      • සෞඛ්‍ය සම්පන්නතාව
      • මානසික සන්සුන්කම
      
      විශේෂ ගුණාංග:
      • ත්‍රිත්ව ආශීර්වාද (ධනය, සෞඛ්‍යය, සතුට)
      • රත්‍රන් පැහැති විශේෂ මුද්‍රණය
      • ශක්තිමත් මන්ත්‍ර සහිත
      • පවුල සඳහා ආරක්ෂණ ශක්තිය
    `,
    benefits: [
      'සම්පූර්ණ ජීවිත සමෘද්ධිය',
      'පවුලේ සතුට වැඩි කිරීම',
      'සෞඛ්‍ය ආරක්ෂණය',
      'මානසික සන්සුන්කම',
      'ධනය සහ සතුට එකට'
    ],
    specifications: {
      material: 'ප්‍රිමියම් කාඩ්බෝඩ්',
      size: '9cm x 6cm',
      thickness: '1mm',
      finish: 'රත්‍රන් පැහැති ස්පෙෂල් කෝටිං',
      packaging: 'සිල්ක් කොටුව සහිත'
    },
    images: [
      '/god.jpg',
      '/god.jpg',
      '/god.jpg'
    ],
    inStock: true,
    featured: true,
    rating: 4.9,
    reviewCount: 203
  },
  {
    id: 'kubera-business-card',
    name: 'කුබේර ව්‍යාපාර කාඩ්පත',
    englishName: 'Kubera Business Card',
    price: 3500,
    originalPrice: 4500,
    discount: 22,
    category: 'business',
    description: 'ව්‍යාපාරික සාර්ථකත්වය සහ වෘත්තීය දියුණුව සඳහා විශේෂයෙන් නිර්මාණය කරන ලද කුබේර කාඩ්පත. ව්‍යාපාරිකයන් සහ වෘත්තිකයන් සඳහා අත්‍යවශ්‍ය.',
    longDescription: `
      කුබේර ව්‍යාපාර කාඩ්පත යනු ව්‍යාපාරික ක්ෂේත්‍රයේ සාර්ථකත්වය සඳහා විශේෂයෙන් සකස් කරන ලද ශක්තිමත් අධ්‍යාත්මික මෙවලමකි.
      
      ව්‍යාපාරික ප්‍රතිලාභ:
      • නව ගනුදෙනුකරුවන් ආකර්ෂණය
      • ව්‍යාපාරික අවස්ථා වැඩි කිරීම
      • තරඟකාරිත්වය වැඩි කිරීම
      • ලාභදායකත්වය වැඩි කිරීම
      
      වෘත්තීය ප්‍රතිලාභ:
      • කැරියර් දියුණුව
      • වැටුප් වැඩි කිරීම
      • නායකත්ව ගුණාංග වර්ධනය
      • කාර්යක්ෂේත්‍රයේ පිළිගැනීම
    `,
    benefits: [
      'ව්‍යාපාරික සාර්ථකත්වය',
      'නව අවස්ථා ලැබීම',
      'ගනුදෙනුකරුවන් වැඩි කිරීම',
      'ලාභදායකත්වය වැඩි කිරීම',
      'වෘත්තීය දියුණුව'
    ],
    specifications: {
      material: 'ප්‍රිමියම් ප්ලාස්ටික් කාඩ්',
      size: '8.5cm x 5.5cm',
      thickness: '0.76mm',
      finish: 'මැට් ගෝල්ඩ් ෆිනිෂ්',
      packaging: 'ලක්ෂරි ලෙදර් කේස්'
    },
    images: [
      '/god.jpg',
      '/god.jpg',
      '/god.jpg'
    ],
    inStock: true,
    featured: false,
    rating: 4.7,
    reviewCount: 89
  },
  {
    id: 'kubera-protection-card',
    name: 'කුබේර ආරක්ෂණ කාඩ්පත',
    englishName: 'Kubera Protection Card',
    price: 2000,
    originalPrice: 2800,
    discount: 29,
    category: 'protection',
    description: 'ධනය සහ සම්පත් ආරක්ෂා කිරීම සඳහා විශේෂ ශක්තියක් ඇති කුබේර කාඩ්පත. අනවශ්‍ය වියදම් සහ ධන හානි වළක්වයි.',
    longDescription: `
      කුබේර ආරක්ෂණ කාඩ්පත යනු ඔබගේ ධනය සහ සම්පත් ආරක්ෂා කිරීම සඳහා නිර්මාණය කරන ලද විශේෂ කාඩ්පතකි.
      
      ආරක්ෂණ ප්‍රතිලාභ:
      • අනවශ්‍ය වියදම් වළක්වයි
      • ධන හානි වළක්වයි
      • වංචා වලින් ආරක්ෂා කරයි
      • ආර්ථික ස්ථාවරත්වය රකියි
      
      අතිරේක ප්‍රතිලාභ:
      • මානසික සන්සුන්කම
      • ආර්ථික තීරණ ගැනීමේ ශක්තිය
      • ඉතිරි කිරීමේ හැකියාව
      • ආර්ථික සැලසුම් කිරීමේ ශක්තිය
    `,
    benefits: [
      'ධන ආරක්ෂණය',
      'අනවශ්‍ය වියදම් වළක්වයි',
      'වංචා වලින් ආරක්ෂා',
      'ඉතිරි කිරීමේ හැකියාව',
      'ආර්ථික ස්ථාවරත්වය'
    ],
    specifications: {
      material: 'ස්ටෑන්ඩර්ඩ් කාඩ්බෝඩ්',
      size: '8cm x 5cm',
      thickness: '0.7mm',
      finish: 'සිල්වර් ෆොයිල් ප්‍රින්ට්',
      packaging: 'සරල ආරක්ෂිත කවරය'
    },
    images: [
      '/god.jpg',
      '/god.jpg',
      '/god.jpg'
    ],
    inStock: true,
    featured: false,
    rating: 4.6,
    reviewCount: 67
  }
];

// Card categories for filtering
export const cardCategories = [
  { id: 'all', name: 'සියලුම කාඩ්පත්', englishName: 'All Cards' },
  { id: 'wealth', name: 'ධන කාඩ්පත්', englishName: 'Wealth Cards' },
  { id: 'prosperity', name: 'සමෘද්ධි කාඩ්පත්', englishName: 'Prosperity Cards' },
  { id: 'business', name: 'ව්‍යාපාර කාඩ්පත්', englishName: 'Business Cards' },
  { id: 'protection', name: 'ආරක්ෂණ කාඩ්පත්', englishName: 'Protection Cards' }
];

// Helper functions
export const getFeaturedCards = () => kuberaCards.filter(card => card.featured);
export const getCardById = (id) => kuberaCards.find(card => card.id === id);
export const getCardsByCategory = (category) => 
  category === 'all' ? kuberaCards : kuberaCards.filter(card => card.category === category);
export const calculateDiscountedPrice = (originalPrice, discount) => 
  Math.round(originalPrice * (1 - discount / 100));
