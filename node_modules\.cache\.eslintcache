[{"C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\LandingPage.js": "1", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\index.js": "2", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\App.js": "3", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\ZodiacPage.js": "4", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\ParticleBackground.js": "5", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\KuberaAnimation.js": "6", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\SmokeAnimation.js": "7", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\services\\HoroscopeService.js": "8", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\KuberaCardsPage.js": "9", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\CheckoutPage.js": "10", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\OrderConfirmationPage.js": "11", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\ProductDetailsPage.js": "12", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\CartPage.js": "13", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\context\\CartContext.js": "14", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\CartButton.js": "15", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\data\\kuberaCards.js": "16"}, {"size": 23071, "mtime": 1751391159979, "results": "17", "hashOfConfig": "18"}, {"size": 253, "mtime": 1750790409543, "results": "19", "hashOfConfig": "18"}, {"size": 4128, "mtime": 1751386409283, "results": "20", "hashOfConfig": "18"}, {"size": 27570, "mtime": 1751103369845, "results": "21", "hashOfConfig": "18"}, {"size": 3837, "mtime": 1750614946000, "results": "22", "hashOfConfig": "18"}, {"size": 7496, "mtime": 1750614946000, "results": "23", "hashOfConfig": "18"}, {"size": 2802, "mtime": 1750614946000, "results": "24", "hashOfConfig": "18"}, {"size": 6127, "mtime": 1751389760889, "results": "25", "hashOfConfig": "18"}, {"size": 6091, "mtime": 1751385700124, "results": "26", "hashOfConfig": "18"}, {"size": 19789, "mtime": 1751390560328, "results": "27", "hashOfConfig": "18"}, {"size": 13792, "mtime": 1751390774903, "results": "28", "hashOfConfig": "18"}, {"size": 14179, "mtime": 1751386899105, "results": "29", "hashOfConfig": "18"}, {"size": 7840, "mtime": 1751385832047, "results": "30", "hashOfConfig": "18"}, {"size": 4359, "mtime": 1751385780141, "results": "31", "hashOfConfig": "18"}, {"size": 816, "mtime": 1751386426126, "results": "32", "hashOfConfig": "18"}, {"size": 11145, "mtime": 1751384942836, "results": "33", "hashOfConfig": "18"}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "cwcvdy", {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\LandingPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\ZodiacPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\ParticleBackground.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\KuberaAnimation.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\SmokeAnimation.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\services\\HoroscopeService.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\KuberaCardsPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\CheckoutPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\OrderConfirmationPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\ProductDetailsPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\CartPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\context\\CartContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\CartButton.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\data\\kuberaCards.js", [], []]