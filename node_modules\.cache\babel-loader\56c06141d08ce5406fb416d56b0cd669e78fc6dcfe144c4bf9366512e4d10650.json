{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Horoscope\\\\src\\\\context\\\\CartContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useReducer, useEffect } from 'react';\n\n// Cart Context\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CartContext = /*#__PURE__*/createContext();\n\n// Cart Actions\nconst CART_ACTIONS = {\n  ADD_ITEM: 'ADD_ITEM',\n  REMOVE_ITEM: 'REMOVE_ITEM',\n  UPDATE_QUANTITY: 'UPDATE_QUANTITY',\n  CLEAR_CART: 'CLEAR_CART',\n  LOAD_CART: 'LOAD_CART'\n};\n\n// Cart Reducer\nconst cartReducer = (state, action) => {\n  switch (action.type) {\n    case CART_ACTIONS.ADD_ITEM:\n      {\n        const {\n          item\n        } = action.payload;\n        const existingItemIndex = state.items.findIndex(cartItem => cartItem.id === item.id);\n        if (existingItemIndex > -1) {\n          // Update quantity if item exists\n          const updatedItems = [...state.items];\n          updatedItems[existingItemIndex].quantity += item.quantity;\n          return {\n            ...state,\n            items: updatedItems\n          };\n        } else {\n          // Add new item\n          return {\n            ...state,\n            items: [...state.items, item]\n          };\n        }\n      }\n    case CART_ACTIONS.REMOVE_ITEM:\n      {\n        const {\n          itemId\n        } = action.payload;\n        return {\n          ...state,\n          items: state.items.filter(item => item.id !== itemId)\n        };\n      }\n    case CART_ACTIONS.UPDATE_QUANTITY:\n      {\n        const {\n          itemId,\n          quantity\n        } = action.payload;\n        if (quantity <= 0) {\n          return {\n            ...state,\n            items: state.items.filter(item => item.id !== itemId)\n          };\n        }\n        const updatedItems = state.items.map(item => item.id === itemId ? {\n          ...item,\n          quantity\n        } : item);\n        return {\n          ...state,\n          items: updatedItems\n        };\n      }\n    case CART_ACTIONS.CLEAR_CART:\n      {\n        return {\n          ...state,\n          items: []\n        };\n      }\n    case CART_ACTIONS.LOAD_CART:\n      {\n        return {\n          ...state,\n          items: action.payload.items || []\n        };\n      }\n    default:\n      return state;\n  }\n};\n\n// Initial cart state\nconst initialCartState = {\n  items: []\n};\n\n// Cart Provider Component\nexport const CartProvider = ({\n  children\n}) => {\n  _s();\n  const [cartState, dispatch] = useReducer(cartReducer, initialCartState);\n\n  // Load cart from localStorage on mount\n  useEffect(() => {\n    const savedCart = localStorage.getItem('kuberaCart');\n    if (savedCart) {\n      try {\n        const parsedCart = JSON.parse(savedCart);\n        dispatch({\n          type: CART_ACTIONS.LOAD_CART,\n          payload: {\n            items: parsedCart\n          }\n        });\n      } catch (error) {\n        console.error('Error loading cart from localStorage:', error);\n      }\n    }\n  }, []);\n\n  // Save cart to localStorage whenever cart changes\n  useEffect(() => {\n    localStorage.setItem('kuberaCart', JSON.stringify(cartState.items));\n  }, [cartState.items]);\n\n  // Cart actions\n  const addToCart = item => {\n    dispatch({\n      type: CART_ACTIONS.ADD_ITEM,\n      payload: {\n        item\n      }\n    });\n  };\n  const removeFromCart = itemId => {\n    dispatch({\n      type: CART_ACTIONS.REMOVE_ITEM,\n      payload: {\n        itemId\n      }\n    });\n  };\n  const updateQuantity = (itemId, quantity) => {\n    dispatch({\n      type: CART_ACTIONS.UPDATE_QUANTITY,\n      payload: {\n        itemId,\n        quantity\n      }\n    });\n  };\n  const clearCart = () => {\n    dispatch({\n      type: CART_ACTIONS.CLEAR_CART\n    });\n  };\n\n  // Cart calculations\n  const getCartTotal = () => {\n    return cartState.items.reduce((total, item) => total + item.price * item.quantity, 0);\n  };\n  const getCartItemCount = () => {\n    return cartState.items.reduce((count, item) => count + item.quantity, 0);\n  };\n  const getCartItemById = itemId => {\n    return cartState.items.find(item => item.id === itemId);\n  };\n  const isItemInCart = itemId => {\n    return cartState.items.some(item => item.id === itemId);\n  };\n\n  // Context value\n  const contextValue = {\n    cart: cartState,\n    addToCart,\n    removeFromCart,\n    updateQuantity,\n    clearCart,\n    getCartTotal,\n    getCartItemCount,\n    getCartItemById,\n    isItemInCart\n  };\n  return /*#__PURE__*/_jsxDEV(CartContext.Provider, {\n    value: contextValue,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 174,\n    columnNumber: 5\n  }, this);\n};\n\n// Custom hook to use cart context\n_s(CartProvider, \"1Z0iH7Y6Ozpqpg0Vf/ELRnmWHhM=\");\n_c = CartProvider;\nexport const useCart = () => {\n  _s2();\n  const context = useContext(CartContext);\n  if (!context) {\n    throw new Error('useCart must be used within a CartProvider');\n  }\n  return context;\n};\n\n// Export cart actions for external use\n_s2(useCart, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport { CART_ACTIONS };\nvar _c;\n$RefreshReg$(_c, \"CartProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useReducer", "useEffect", "jsxDEV", "_jsxDEV", "CartContext", "CART_ACTIONS", "ADD_ITEM", "REMOVE_ITEM", "UPDATE_QUANTITY", "CLEAR_CART", "LOAD_CART", "cartReducer", "state", "action", "type", "item", "payload", "existingItemIndex", "items", "findIndex", "cartItem", "id", "updatedItems", "quantity", "itemId", "filter", "map", "initialCartState", "CartProvider", "children", "_s", "cartState", "dispatch", "savedCart", "localStorage", "getItem", "parsedCart", "JSON", "parse", "error", "console", "setItem", "stringify", "addToCart", "removeFromCart", "updateQuantity", "clearCart", "getCartTotal", "reduce", "total", "price", "getCartItemCount", "count", "getCartItemById", "find", "isItemInCart", "some", "contextValue", "cart", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useCart", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/src/context/CartContext.js"], "sourcesContent": ["import React, { createContext, useContext, useReducer, useEffect } from 'react';\n\n// Cart Context\nconst CartContext = createContext();\n\n// Cart Actions\nconst CART_ACTIONS = {\n  ADD_ITEM: 'ADD_ITEM',\n  REMOVE_ITEM: 'REMOVE_ITEM',\n  UPDATE_QUANTITY: 'UPDATE_QUANTITY',\n  CLEAR_CART: 'CLEAR_CART',\n  LOAD_CART: 'LOAD_CART'\n};\n\n// Cart Reducer\nconst cartReducer = (state, action) => {\n  switch (action.type) {\n    case CART_ACTIONS.ADD_ITEM: {\n      const { item } = action.payload;\n      const existingItemIndex = state.items.findIndex(cartItem => cartItem.id === item.id);\n      \n      if (existingItemIndex > -1) {\n        // Update quantity if item exists\n        const updatedItems = [...state.items];\n        updatedItems[existingItemIndex].quantity += item.quantity;\n        return {\n          ...state,\n          items: updatedItems\n        };\n      } else {\n        // Add new item\n        return {\n          ...state,\n          items: [...state.items, item]\n        };\n      }\n    }\n    \n    case CART_ACTIONS.REMOVE_ITEM: {\n      const { itemId } = action.payload;\n      return {\n        ...state,\n        items: state.items.filter(item => item.id !== itemId)\n      };\n    }\n    \n    case CART_ACTIONS.UPDATE_QUANTITY: {\n      const { itemId, quantity } = action.payload;\n      if (quantity <= 0) {\n        return {\n          ...state,\n          items: state.items.filter(item => item.id !== itemId)\n        };\n      }\n      \n      const updatedItems = state.items.map(item =>\n        item.id === itemId ? { ...item, quantity } : item\n      );\n      \n      return {\n        ...state,\n        items: updatedItems\n      };\n    }\n    \n    case CART_ACTIONS.CLEAR_CART: {\n      return {\n        ...state,\n        items: []\n      };\n    }\n    \n    case CART_ACTIONS.LOAD_CART: {\n      return {\n        ...state,\n        items: action.payload.items || []\n      };\n    }\n    \n    default:\n      return state;\n  }\n};\n\n// Initial cart state\nconst initialCartState = {\n  items: []\n};\n\n// Cart Provider Component\nexport const CartProvider = ({ children }) => {\n  const [cartState, dispatch] = useReducer(cartReducer, initialCartState);\n\n  // Load cart from localStorage on mount\n  useEffect(() => {\n    const savedCart = localStorage.getItem('kuberaCart');\n    if (savedCart) {\n      try {\n        const parsedCart = JSON.parse(savedCart);\n        dispatch({\n          type: CART_ACTIONS.LOAD_CART,\n          payload: { items: parsedCart }\n        });\n      } catch (error) {\n        console.error('Error loading cart from localStorage:', error);\n      }\n    }\n  }, []);\n\n  // Save cart to localStorage whenever cart changes\n  useEffect(() => {\n    localStorage.setItem('kuberaCart', JSON.stringify(cartState.items));\n  }, [cartState.items]);\n\n  // Cart actions\n  const addToCart = (item) => {\n    dispatch({\n      type: CART_ACTIONS.ADD_ITEM,\n      payload: { item }\n    });\n  };\n\n  const removeFromCart = (itemId) => {\n    dispatch({\n      type: CART_ACTIONS.REMOVE_ITEM,\n      payload: { itemId }\n    });\n  };\n\n  const updateQuantity = (itemId, quantity) => {\n    dispatch({\n      type: CART_ACTIONS.UPDATE_QUANTITY,\n      payload: { itemId, quantity }\n    });\n  };\n\n  const clearCart = () => {\n    dispatch({\n      type: CART_ACTIONS.CLEAR_CART\n    });\n  };\n\n  // Cart calculations\n  const getCartTotal = () => {\n    return cartState.items.reduce((total, item) => total + (item.price * item.quantity), 0);\n  };\n\n  const getCartItemCount = () => {\n    return cartState.items.reduce((count, item) => count + item.quantity, 0);\n  };\n\n  const getCartItemById = (itemId) => {\n    return cartState.items.find(item => item.id === itemId);\n  };\n\n  const isItemInCart = (itemId) => {\n    return cartState.items.some(item => item.id === itemId);\n  };\n\n  // Context value\n  const contextValue = {\n    cart: cartState,\n    addToCart,\n    removeFromCart,\n    updateQuantity,\n    clearCart,\n    getCartTotal,\n    getCartItemCount,\n    getCartItemById,\n    isItemInCart\n  };\n\n  return (\n    <CartContext.Provider value={contextValue}>\n      {children}\n    </CartContext.Provider>\n  );\n};\n\n// Custom hook to use cart context\nexport const useCart = () => {\n  const context = useContext(CartContext);\n  if (!context) {\n    throw new Error('useCart must be used within a CartProvider');\n  }\n  return context;\n};\n\n// Export cart actions for external use\nexport { CART_ACTIONS };\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;;AAE/E;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,WAAW,gBAAGN,aAAa,CAAC,CAAC;;AAEnC;AACA,MAAMO,YAAY,GAAG;EACnBC,QAAQ,EAAE,UAAU;EACpBC,WAAW,EAAE,aAAa;EAC1BC,eAAe,EAAE,iBAAiB;EAClCC,UAAU,EAAE,YAAY;EACxBC,SAAS,EAAE;AACb,CAAC;;AAED;AACA,MAAMC,WAAW,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;EACrC,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKT,YAAY,CAACC,QAAQ;MAAE;QAC1B,MAAM;UAAES;QAAK,CAAC,GAAGF,MAAM,CAACG,OAAO;QAC/B,MAAMC,iBAAiB,GAAGL,KAAK,CAACM,KAAK,CAACC,SAAS,CAACC,QAAQ,IAAIA,QAAQ,CAACC,EAAE,KAAKN,IAAI,CAACM,EAAE,CAAC;QAEpF,IAAIJ,iBAAiB,GAAG,CAAC,CAAC,EAAE;UAC1B;UACA,MAAMK,YAAY,GAAG,CAAC,GAAGV,KAAK,CAACM,KAAK,CAAC;UACrCI,YAAY,CAACL,iBAAiB,CAAC,CAACM,QAAQ,IAAIR,IAAI,CAACQ,QAAQ;UACzD,OAAO;YACL,GAAGX,KAAK;YACRM,KAAK,EAAEI;UACT,CAAC;QACH,CAAC,MAAM;UACL;UACA,OAAO;YACL,GAAGV,KAAK;YACRM,KAAK,EAAE,CAAC,GAAGN,KAAK,CAACM,KAAK,EAAEH,IAAI;UAC9B,CAAC;QACH;MACF;IAEA,KAAKV,YAAY,CAACE,WAAW;MAAE;QAC7B,MAAM;UAAEiB;QAAO,CAAC,GAAGX,MAAM,CAACG,OAAO;QACjC,OAAO;UACL,GAAGJ,KAAK;UACRM,KAAK,EAAEN,KAAK,CAACM,KAAK,CAACO,MAAM,CAACV,IAAI,IAAIA,IAAI,CAACM,EAAE,KAAKG,MAAM;QACtD,CAAC;MACH;IAEA,KAAKnB,YAAY,CAACG,eAAe;MAAE;QACjC,MAAM;UAAEgB,MAAM;UAAED;QAAS,CAAC,GAAGV,MAAM,CAACG,OAAO;QAC3C,IAAIO,QAAQ,IAAI,CAAC,EAAE;UACjB,OAAO;YACL,GAAGX,KAAK;YACRM,KAAK,EAAEN,KAAK,CAACM,KAAK,CAACO,MAAM,CAACV,IAAI,IAAIA,IAAI,CAACM,EAAE,KAAKG,MAAM;UACtD,CAAC;QACH;QAEA,MAAMF,YAAY,GAAGV,KAAK,CAACM,KAAK,CAACQ,GAAG,CAACX,IAAI,IACvCA,IAAI,CAACM,EAAE,KAAKG,MAAM,GAAG;UAAE,GAAGT,IAAI;UAAEQ;QAAS,CAAC,GAAGR,IAC/C,CAAC;QAED,OAAO;UACL,GAAGH,KAAK;UACRM,KAAK,EAAEI;QACT,CAAC;MACH;IAEA,KAAKjB,YAAY,CAACI,UAAU;MAAE;QAC5B,OAAO;UACL,GAAGG,KAAK;UACRM,KAAK,EAAE;QACT,CAAC;MACH;IAEA,KAAKb,YAAY,CAACK,SAAS;MAAE;QAC3B,OAAO;UACL,GAAGE,KAAK;UACRM,KAAK,EAAEL,MAAM,CAACG,OAAO,CAACE,KAAK,IAAI;QACjC,CAAC;MACH;IAEA;MACE,OAAON,KAAK;EAChB;AACF,CAAC;;AAED;AACA,MAAMe,gBAAgB,GAAG;EACvBT,KAAK,EAAE;AACT,CAAC;;AAED;AACA,OAAO,MAAMU,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC5C,MAAM,CAACC,SAAS,EAAEC,QAAQ,CAAC,GAAGhC,UAAU,CAACW,WAAW,EAAEgB,gBAAgB,CAAC;;EAEvE;EACA1B,SAAS,CAAC,MAAM;IACd,MAAMgC,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IACpD,IAAIF,SAAS,EAAE;MACb,IAAI;QACF,MAAMG,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACL,SAAS,CAAC;QACxCD,QAAQ,CAAC;UACPlB,IAAI,EAAET,YAAY,CAACK,SAAS;UAC5BM,OAAO,EAAE;YAAEE,KAAK,EAAEkB;UAAW;QAC/B,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC/D;IACF;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAtC,SAAS,CAAC,MAAM;IACdiC,YAAY,CAACO,OAAO,CAAC,YAAY,EAAEJ,IAAI,CAACK,SAAS,CAACX,SAAS,CAACb,KAAK,CAAC,CAAC;EACrE,CAAC,EAAE,CAACa,SAAS,CAACb,KAAK,CAAC,CAAC;;EAErB;EACA,MAAMyB,SAAS,GAAI5B,IAAI,IAAK;IAC1BiB,QAAQ,CAAC;MACPlB,IAAI,EAAET,YAAY,CAACC,QAAQ;MAC3BU,OAAO,EAAE;QAAED;MAAK;IAClB,CAAC,CAAC;EACJ,CAAC;EAED,MAAM6B,cAAc,GAAIpB,MAAM,IAAK;IACjCQ,QAAQ,CAAC;MACPlB,IAAI,EAAET,YAAY,CAACE,WAAW;MAC9BS,OAAO,EAAE;QAAEQ;MAAO;IACpB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMqB,cAAc,GAAGA,CAACrB,MAAM,EAAED,QAAQ,KAAK;IAC3CS,QAAQ,CAAC;MACPlB,IAAI,EAAET,YAAY,CAACG,eAAe;MAClCQ,OAAO,EAAE;QAAEQ,MAAM;QAAED;MAAS;IAC9B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMuB,SAAS,GAAGA,CAAA,KAAM;IACtBd,QAAQ,CAAC;MACPlB,IAAI,EAAET,YAAY,CAACI;IACrB,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMsC,YAAY,GAAGA,CAAA,KAAM;IACzB,OAAOhB,SAAS,CAACb,KAAK,CAAC8B,MAAM,CAAC,CAACC,KAAK,EAAElC,IAAI,KAAKkC,KAAK,GAAIlC,IAAI,CAACmC,KAAK,GAAGnC,IAAI,CAACQ,QAAS,EAAE,CAAC,CAAC;EACzF,CAAC;EAED,MAAM4B,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,OAAOpB,SAAS,CAACb,KAAK,CAAC8B,MAAM,CAAC,CAACI,KAAK,EAAErC,IAAI,KAAKqC,KAAK,GAAGrC,IAAI,CAACQ,QAAQ,EAAE,CAAC,CAAC;EAC1E,CAAC;EAED,MAAM8B,eAAe,GAAI7B,MAAM,IAAK;IAClC,OAAOO,SAAS,CAACb,KAAK,CAACoC,IAAI,CAACvC,IAAI,IAAIA,IAAI,CAACM,EAAE,KAAKG,MAAM,CAAC;EACzD,CAAC;EAED,MAAM+B,YAAY,GAAI/B,MAAM,IAAK;IAC/B,OAAOO,SAAS,CAACb,KAAK,CAACsC,IAAI,CAACzC,IAAI,IAAIA,IAAI,CAACM,EAAE,KAAKG,MAAM,CAAC;EACzD,CAAC;;EAED;EACA,MAAMiC,YAAY,GAAG;IACnBC,IAAI,EAAE3B,SAAS;IACfY,SAAS;IACTC,cAAc;IACdC,cAAc;IACdC,SAAS;IACTC,YAAY;IACZI,gBAAgB;IAChBE,eAAe;IACfE;EACF,CAAC;EAED,oBACEpD,OAAA,CAACC,WAAW,CAACuD,QAAQ;IAACC,KAAK,EAAEH,YAAa;IAAA5B,QAAA,EACvCA;EAAQ;IAAAgC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;;AAED;AAAAlC,EAAA,CAzFaF,YAAY;AAAAqC,EAAA,GAAZrC,YAAY;AA0FzB,OAAO,MAAMsC,OAAO,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC3B,MAAMC,OAAO,GAAGrE,UAAU,CAACK,WAAW,CAAC;EACvC,IAAI,CAACgE,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,4CAA4C,CAAC;EAC/D;EACA,OAAOD,OAAO;AAChB,CAAC;;AAED;AAAAD,GAAA,CARaD,OAAO;AASpB,SAAS7D,YAAY;AAAG,IAAA4D,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}